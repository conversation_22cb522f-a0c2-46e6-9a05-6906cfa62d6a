import torch
import numpy as np
import sqlite3
import os
from sacd.model import CateoricalPolicy

def load_policy_model_auto(model_path):
    """
    自动从保存的模型中获取网络结构参数并加载策略模型
    """
    # 加载模型权重
    state_dict = torch.load(model_path, map_location='cpu')
    
    # 自动查找第一层和最后一层
    first_layer_weight = None
    last_layer_weight = None
    
    # 收集所有线性层的权重
    linear_weights = {}
    for key, value in state_dict.items():
        if 'weight' in key and 'head' in key:
            # 提取层索引
            layer_idx = int(key.split('.')[1])
            linear_weights[layer_idx] = value
            
    if not linear_weights:
        raise ValueError("未找到线性层权重")
    
    # 获取最后一层（索引最大的层）
    first_layer_idx = min(linear_weights.keys())
    first_layer_weight = linear_weights[first_layer_idx]
    last_layer_idx = max(linear_weights.keys())
    last_layer_weight = linear_weights[last_layer_idx]
    
    print(f"🔍 自动检测的网络结构:")
    print(f"   第一层: head.{first_layer_idx}.weight -> {first_layer_weight.shape}")
    print(f"   最后一层: head.{last_layer_idx}.weight -> {last_layer_weight.shape}")
    print(f"   总层数: {len(linear_weights)} 个线性层")
    
    # 推断参数
    state_dim = first_layer_weight.shape[1]  # 输入维度
    num_actions = last_layer_weight.shape[0]  # 输出维度
    
    print(f"   推断参数: state_dim={state_dim}, num_actions={num_actions}")
    print(f"   使用默认正则化参数\n")
    
    # 创建策略网络（使用默认参数）
    policy = CateoricalPolicy(state_dim=state_dim, num_actions=num_actions)
    
    # 加载权重
    policy.load_state_dict(state_dict)
    policy.eval()
    
    return policy

def get_all_test_states(db_path='data/test_data.db'):
    """
    从测试数据库中获取所有唯一的状态
    
    Args:
        db_path: 测试数据库路径
    
    Returns:
        list: 所有唯一的状态列表
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有唯一的状态组合
    query = '''
    SELECT DISTINCT "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
    FROM test_table
    ORDER BY "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
    '''
    
    cursor.execute(query)
    states = cursor.fetchall()
    
    # 转换为numpy数组
    states_array = np.array(states, dtype=np.float64)
    
    conn.close()
    
    print(f"📊 从测试数据库中找到 {len(states_array)} 个唯一状态")
    return states_array

def test_model_on_all_states(model_path, db_name='test_data.db', max_states=None):
    """
    在测试数据库的所有状态上测试模型
    
    Args:
        model_path: 模型文件路径
        db_name: 测试数据库文件名
        max_states: 最大测试状态数（None表示测试所有状态）
    
    Returns:
        dict: 测试结果统计
    """
    # 加载模型
    policy = load_policy_model_auto(model_path)
    
    # 获取所有测试状态
    db_path = os.path.join('data', db_name)
    all_states = get_all_test_states(db_path)
    
    # 限制测试状态数量
    if max_states is not None and len(all_states) > max_states:
        all_states = all_states[:max_states]
        print(f"⚠️ 限制测试状态数量为 {max_states}")
    
    print(f"\n🧪 开始测试 {len(all_states)} 个状态...")
    
    # 统计结果
    results = {
        'total_states': len(all_states), # 总状态数
        'actions_taken': [], # 记录每个状态选择的动作
        'action_counts': {}, # 记录每个动作被选择的次数
        'state_action_pairs': [] # 记录每个状态和动作的配对
    }
    
    # 测试每个状态
    for i, state in enumerate(all_states):
        # 使用模型进行预测
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            # 使用贪婪策略选择动作
            action = policy.act(state_tensor)
            action_idx = action.item() + 1
            
            # 获取动作概率分布
            _, action_probs, _ = policy.sample(state_tensor)
            action_probs = action_probs.squeeze().numpy()
        
        # 记录结果
        results['actions_taken'].append(action_idx)
        results['state_action_pairs'].append({
            'state': state.tolist(),
            'action': action_idx,
            'action_probs': action_probs.tolist()
        })
        
        # 统计动作使用频率
        if action_idx not in results['action_counts']:
            results['action_counts'][action_idx] = 0
        results['action_counts'][action_idx] += 1
        
        # 显示进度
        if (i + 1) % 100 == 0 or i == len(all_states) - 1:
            print(f"   进度: {i + 1}/{len(all_states)} ({100 * (i + 1) / len(all_states):.1f}%)")
    
    # 计算统计信息
    unique_actions = len(results['action_counts'])
    most_common_action = max(results['action_counts'].items(), key=lambda x: x[1])
    least_common_action = min(results['action_counts'].items(), key=lambda x: x[1])
    
    print(f"\n📈 测试结果统计:")
    print(f"   测试状态数: {results['total_states']}")
    print(f"   使用的不同动作数: {unique_actions}")
    print(f"   最常用动作: {most_common_action[0]} (使用 {most_common_action[1]} 次, {100 * most_common_action[1] / results['total_states']:.1f}%)")
    print(f"   最少用动作: {least_common_action[0]} (使用 {least_common_action[1]} 次, {100 * least_common_action[1] / results['total_states']:.1f}%)")
    
    # 显示前10个最常用的动作
    sorted_actions = sorted(results['action_counts'].items(), key=lambda x: x[1], reverse=True)
    print(f"\n🏆 前10个最常用动作:")
    for i, (action, count) in enumerate(sorted_actions[:10]):
        percentage = 100 * count / results['total_states']
        print(f"   {i+1:2d}. 动作 {action:3d}: {count:4d} 次 ({percentage:5.1f}%)")
    
    return results


# 主测试函数
if __name__ == "__main__":
    # 模型路径
    model_path = 'logs/sacd-seed0-20250703-111238/model/best/policy.pth'
    db_name = 'test_data.db'
    
    try:
        print("🚀 开始全面测试模型...")
        
        # 1. 测试所有状态（限制数量以避免时间过长）
        print("\n" + "="*60)
        print("📊 测试1: 在所有测试状态上测试模型")
        print("="*60)
        all_states_results = test_model_on_all_states(
            model_path, 
            db_name=db_name, 
            max_states=1000,  # 限制为1000个状态，你可以调整这个数字
        )
    except FileNotFoundError:
        print(f"❌ 错误: 找不到模型文件 {model_path}")
        print("请检查文件路径是否正确")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
