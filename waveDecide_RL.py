import torch
import numpy as np
import sqlite3
import os

from sacd.model import CateoricalPolicy

def load_policy_model_auto(model_path):
    """
    自动从保存的模型中获取网络结构参数并加载策略模型
    """
    # 路径判断
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件未找到: {model_path}")

    # 加载模型权重
    state_dict = torch.load(model_path, map_location='cuda')
    
    # 自动查找第一层和最后一层
    first_layer_weight = None
    last_layer_weight = None
    
    # 收集所有线性层的权重
    linear_weights = {}
    for key, value in state_dict.items():
        if 'weight' in key and 'head' in key:
            # 提取层索引
            layer_idx = int(key.split('.')[1])
            linear_weights[layer_idx] = value
            
    if not linear_weights:
        raise ValueError("未找到线性层权重")
    
    # 获取最后一层（索引最大的层）
    first_layer_idx = min(linear_weights.keys())
    first_layer_weight = linear_weights[first_layer_idx]
    last_layer_idx = max(linear_weights.keys())
    last_layer_weight = linear_weights[last_layer_idx]
    
    # print(f"🔍 自动检测的网络结构:")
    # print(f"   第一层: head.{first_layer_idx}.weight -> {first_layer_weight.shape}")
    # print(f"   最后一层: head.{last_layer_idx}.weight -> {last_layer_weight.shape}")
    # print(f"   总层数: {len(linear_weights)} 个线性层")
    
    # 推断参数
    state_dim = first_layer_weight.shape[1]  # 输入维度
    num_actions = last_layer_weight.shape[0]  # 输出维度
    
    # print(f"   推断参数: state_dim={state_dim}, num_actions={num_actions}")
    
    # 创建策略网络（使用默认参数）
    policy = CateoricalPolicy(state_dim=state_dim, num_actions=num_actions)
    
    # 加载权重
    policy.load_state_dict(state_dict)
    policy.eval()
    
    return policy

def get_all_test_states(db_path='data/test_data.db'):
    """
    从测试数据库中获取所有唯一的状态
    
    Args:
        db_path: 测试数据库路径
    
    Returns:
        list: 所有唯一的状态列表
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有唯一的状态组合
    query = '''
    SELECT DISTINCT "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
    FROM test_table
    ORDER BY "feature1", "feature2", "feature3", "feature4", "feature5", "feature6"
    '''
    
    cursor.execute(query)
    states = cursor.fetchall()
    
    # 转换为numpy数组
    states_array = np.array(states, dtype=np.float64)
    
    conn.close()
    
    # print(f"📊 从测试数据库中找到 {len(states_array)} 个唯一状态")
    return states_array

def waveDecide_RL(interference, Normalized = False):
    policy = load_policy_model_auto('logs/sacd-seed0-20250703-111238/model/best/policy.pth')

    system = int(interference[0])
    style = int(interference[1])
    module = int(interference[2])
    freq = interference[3]
    bandwidth = interference[4]
    power = interference[5]

    if not Normalized:
        freq = np.round(np.float64((freq - 3e7) / (25e8 - 3e7)), 6)
        bandwidth = np.round(np.float64(bandwidth / 655e6), 6)
        power = np.round(np.float64((power - (-100)) / (100 - (-100))), 6)
    if module == None:  # 如果模块为空，则设置为0
        module = 0

    state = np.array([system, style, module, freq, bandwidth, power])
    state = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
    with torch.no_grad():
        action = policy.act(state) + 1
    return action.item()

def verify_in_db_efficient(state, action, cursor):
    """
    验证模型决策的动作是否为给定状态下的任一最优动作（性能指标越小越好）。

    Args:
        state (list/np.ndarray): 包含6个特征的状态。
        action (float/int): 模型决策的1个动作维度的动作。
        cursor (sqlite3.Cursor): 已经连接到数据库的游标。

    Returns:
        bool: 如果决策出的动作是任一性能最好的动作，则返回 True，否则返回 False。
    """
    try:
        # 1. 查询给定状态下的所有动作及其性能指标
        query = '''
        SELECT "action", "performance"
        FROM test_table
        WHERE "feature1"=? AND "feature2"=? AND "feature3"=? AND "feature4"=? 
          AND "feature5"=? AND "feature6"=?
        '''
        
        # 参数只包含状态特征
        state_params = tuple(state)
        cursor.execute(query, state_params)
        all_action_performance = cursor.fetchall()

        # 检查是否找到了该状态的任何记录
        if not all_action_performance:
            print(f"警告：未在数据库中找到状态 {state} 的任何记录。")
            return False

        # 2. 找到最小性能值
        best_performance = float('inf')
        for _, performance in all_action_performance:
            if performance < best_performance:
                best_performance = performance
        
        # 3. 收集所有性能等于最小性能值的动作
        best_actions = []
        for a, performance in all_action_performance:
            # 使用一个小的容差值来处理浮点数精度问题，
            # 确保性能值非常接近时也被认为是相等的。
            if abs(performance - best_performance) < 1e-9:
                best_actions.append(a)
        
        # 4. 比较模型决策的动作是否在最优动作列表中
        if action in best_actions:
            return True
        else:
            # print(f"在状态 {state} 下，模型决策的动作 {action} 不是最优动作。")
            # print(f"最优动作列表为：{best_actions}")
            return False

    except Exception as e:
        print(f"验证过程中发生意外错误: {e}")
        return False

if __name__ == "__main__":
    # ================ 测试单个状态 ================
    print("=============== 测试单个状态 ================\n")
    system = '1'
    style = '1'
    module = '3'
    freq = 5e7
    bandwidth = 0
    power = 40
    interference = [system, style, module, freq, bandwidth, power]
    action = waveDecide_RL(interference, Normalized = False)
    print(f"system: {system}, style: {style}, module: {module}, freq: {freq}, bandwidth: {bandwidth}, power: {power}")
    print(f"action: {action}\n")

    # ================ 测试所有状态 ================
    print("=============== 测试集所有状态 ================\n")
    db_path = os.path.join('data', 'test_data.db')
    all_states = get_all_test_states(db_path)
    # for state in all_states:
    #     interference = [state[0], state[1], state[2], state[3], state[4], state[5]]
    #     action = waveDecide_RL(interference, Normalized = True)
    #     print(f"system: {state[0]}, style: {state[1]}, module: {state[2]}, freq: {state[3]}, bandwidth: {state[4]}, power: {state[5]}")
    #     print(f"action: {action}")


    total_correct = 0
    total_evaluated = 0

    conn = None
    cursor = None
    try:
        # 在循环开始前，建立一次数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        for state in all_states:
            # 1. 在循环内部更新 interference 变量
            interference = [state[0], state[1], state[2], state[3], state[4], state[5]]

            # 2. 让模型生成动作
            with torch.no_grad():
                action = waveDecide_RL(interference, Normalized = True)
            
            # 3. 使用新的高效函数进行验证
            is_correct = verify_in_db_efficient(state, action, cursor)
            
            if is_correct:
                total_correct += 1
            
            total_evaluated += 1
            # print(f"""network_size: {network_size}, neighbor_count: {neighbor_count}, longitude: {longitude}, latitude: {latitude},
            #     avg_connectivity: {avg_connectivity}, neighbor_change_rate: {neighbor_change_rate}, data_rate: {data_rate}, protocol: {protocol}""")
            # print(f"action: {action}")

        # 计算正确率
        accuracy = total_correct / total_evaluated
        print(f"模型在测试数据库上的正确率为: {accuracy:.2f}")
        print(f"total_correct: {total_correct}, total_evaluated: {total_evaluated}")
    except Exception as e:
        print(f"评估循环中发生错误: {e}")

    finally:
        # 在所有工作完成后，统一关闭连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()



    # ================ 测试输入状态 ================
    print("=============== 测试输入状态 ================\n")
    while True:
        user_input = input("请输入六个参数(system, style, module, freq, bandwidth, power),用空格分隔,或输入q退出: ")
        if user_input.strip().lower() == 'q':
            print("已退出。")
            break
        try:
            system, style, module, freq, bandwidth, power = map(float, user_input.strip().split())
        except ValueError:
            print("输入格式有误，请重新输入。")
            continue

        interference = [system, style, module, freq, bandwidth, power]
        action = waveDecide_RL(interference, Normalized = False)
        print(f"动作: {action}\n")
