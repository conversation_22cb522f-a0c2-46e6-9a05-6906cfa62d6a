import tkinter as tk
from tkinter import messagebox
import sqlite3
import os
import torch
import numpy as np
import csv

# 请确保你的核心函数都在 waveDecide_RL.py 文件中
from waveDecide_RL import waveDecide_RL, get_all_test_states, verify_in_db_efficient

class WaveformLookup:
    def __init__(self, csv_file_path="waveform_table.csv"):
        """
        初始化波形查询类
        
        Args:
            csv_file_path: CSV文件路径，默认为当前目录下的waveform_table.csv
        """
        self.waveform_dict = {}
        self.load_waveform_data(csv_file_path)
    
    def load_waveform_data(self, csv_file_path):
        """
        从CSV文件加载波形数据
        
        Args:
            csv_file_path: CSV文件路径
        """
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    waveform_index = int(row['波形索引'])
                    self.waveform_dict[waveform_index] = {
                        '调制方式': row['调制方式'],
                        '工作模式': row['工作模式'],
                        '工作频段': row['工作频段'],
                        '信道带宽': row['信道带宽'],
                        '信息速率': row['信息速率'],
                        '扩频因子': row['扩频因子'],
                        '跳频速率': row['跳频速率'],
                        '跳频频率集': row['跳频频率集'],
                        '编码方式': row['编码方式'],
                        '多址方式': row['多址方式']
                    }
        except FileNotFoundError:
            print(f"错误: 找不到文件 {csv_file_path}")
            raise
        except Exception as e:
            print(f"错误: 读取CSV文件时发生异常: {e}")
            raise
    
    def get_waveform_info(self, action):
        """
        根据波形索引获取波形信息
        
        Args:
            action: 波形索引 (1-100)
            
        Returns:
            dict: 波形参数字典，如果找不到则返回None
        """
        return self.waveform_dict.get(action, None)

class ToolTip:
    """工具提示类，用于在鼠标悬停时显示说明信息"""
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)

    def on_enter(self, event=None):
        if self.tooltip_window or not self.text:
            return
        x, y, _, _ = self.widget.bbox("insert") if hasattr(self.widget, 'bbox') else (0, 0, 0, 0)
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f"+{x}+{y}")

        label = tk.Label(tw, text=self.text, justify=tk.LEFT,
                        background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                        font=("Arial", 9))
        label.pack(ipadx=1)

    def on_leave(self, event=None):
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

class TestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("波形决策模型测试工具")
        self.create_widgets()

    def create_tooltip(self, widget, text):
        """为控件创建工具提示"""
        return ToolTip(widget, text)

    def create_widgets(self):
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        self.results_text = tk.Text(main_frame, height=20, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.results_text.config(state=tk.DISABLED)

        control_frame = tk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        single_test_frame = tk.LabelFrame(control_frame, text="单个测试", padx=10, pady=10)
        single_test_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.single_button = tk.Button(single_test_frame, text="运行单个测试", command=self.run_single_test)
        self.single_button.pack(pady=5)

        batch_test_frame = tk.LabelFrame(control_frame, text="批量测试", padx=10, pady=10)
        batch_test_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.batch_button = tk.Button(batch_test_frame, text="运行批量测试", command=self.run_batch_test)
        self.batch_button.pack(pady=5)

        manual_frame = tk.LabelFrame(main_frame, text="手动输入测试", padx=10, pady=10)
        manual_frame.pack(fill=tk.X, pady=5)

        self.entries = {}
        # 定义标签和对应的说明信息
        field_info = [
            ("干扰体制", "1-3", "1：瞄准式 2：拦阻式 3：跟踪式"),
            ("干扰样式", "1-8", "1：单音 2：多音 3：窄带 4：调频 5：宽带 6：梳状谱 7：扫频 8：跳频"),
            ("调制方式", "1-10", "1:AM 2:FM 3:CW 4:ASK 5:FSK 6:BPSK 7:QPSK 8:8PSK 9:16QAM 10:64QAM"),
            ("干扰信号中心频率", "3e7-25e8 Hz", "干扰信号的中心频率，单位：Hz"),
            ("干扰信号带宽", "0-655e6 Hz", "干扰信号的带宽，单位：Hz"),
            ("干扰信号电平", "-100-100 dBm", "干扰信号的功率电平，单位：dBm")
        ]

        for label_text, range_info, tooltip_text in field_info:
            row_frame = tk.Frame(manual_frame)
            row_frame.pack(fill=tk.X, pady=2)

            # 主标签
            label = tk.Label(row_frame, text=label_text + ":")
            label.pack(side=tk.LEFT, padx=5)

            # 范围说明标签（小字，灰色）
            range_label = tk.Label(row_frame, text=f"({range_info})",
                                 font=("Arial", 8), fg="gray")
            range_label.pack(side=tk.LEFT, padx=2)

            # 输入框
            entry = tk.Entry(row_frame)
            entry.pack(side=tk.RIGHT, expand=True, fill=tk.X, padx=5)

            # 添加工具提示
            self.create_tooltip(entry, tooltip_text)

            self.entries[label_text] = entry

        self.manual_button = tk.Button(manual_frame, text="运行手动测试", command=self.run_manual_test)
        self.manual_button.pack(pady=5)

        # 填充默认值
        self.entries["干扰体制"].insert(0, "1")
        self.entries["干扰样式"].insert(0, "1")
        self.entries["调制方式"].insert(0, "3")
        self.entries["干扰信号中心频率"].insert(0, "5e7")
        self.entries["干扰信号带宽"].insert(0, "0")
        self.entries["干扰信号电平"].insert(0, "40")
    
    def _clear_and_set_title(self, title):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete('1.0', tk.END)
        self.results_text.insert(tk.END, f"================ {title} ================\n\n")

    def format_results(self, interference, action, result_text="", waveform_lookup=None):
        """
        格式化输出结果，包含波形参数信息
        
        Args:
            interference: 干扰参数列表
            action: 波形标签 (1-100)
            result_text: 额外的结果文本
            waveform_lookup: WaveformLookup实例，如果为None则创建新实例
        """
        # 如果没有传入waveform_lookup实例，则创建一个
        if waveform_lookup is None:
            waveform_lookup = WaveformLookup()
        
        # 获取波形信息
        waveform_info = waveform_lookup.get_waveform_info(action)
        
        output_str = (
            f"**测试状态:**\n"
            f"  干扰体制: {interference[0]}\n"
            f"  干扰样式: {interference[1]}\n"
            f"  调制方式: {interference[2]}\n"
            f"  干扰信号中心频率: {interference[3]} Hz\n"
            f"  干扰信号带宽: {interference[4]} Hz\n"
            f"  干扰信号电平: {interference[5]} V\n"
            f"\n"
            f"**模型决策:** \n"
            f"  波形标签: {action} （共100个波形）\n"
        )
        
        # 添加波形参数信息
        if waveform_info:
            output_str += (
                f"\n"
                f"**选择波形参数:**\n"
                f"  调制方式: {waveform_info['调制方式']}\n"
                f"  工作模式: {waveform_info['工作模式']}\n"
                f"  工作频段(Hz): {waveform_info['工作频段']}\n"
                f"  信道带宽(Hz): {waveform_info['信道带宽']}\n"
                f"  信息速率: {waveform_info['信息速率']}\n"
                f"  扩频因子: {waveform_info['扩频因子']}\n"
                f"  跳频速率: {waveform_info['跳频速率']}\n"
                f"  跳频频率集: {waveform_info['跳频频率集']}\n"
                f"  编码方式: {waveform_info['编码方式']}\n"
                f"  多址方式: {waveform_info['多址方式']}\n"
            )
        else:
            output_str += f"\n**选择波形参数:** 未找到波形索引 {action} 对应的参数\n"
        
        output_str += (
            f"\n"
            f"**额外信息**\n"
            f"  干扰体制 1：瞄准式 2：拦阻式 3：跟踪式\n"
            f"  干扰样式 1：单音 2：多音 3：窄带 4：调频 5：宽带 6：梳状谱 7：扫频 8：跳频\n"
            f"  调制方式 1:AM 2:FM 3:CW 4:ASK 5:FSK 6:BPSK 7:QPSK 8:8PSK 9:16QAM 10:64QAM\n"
            f"  干扰信号中心频率(Hz)  3e7 - 25e8\n"
            f"  干扰信号带宽(Hz)  0 - 655e6\n"
            f"  干扰信号电平 (dBm) -100 - 100\n"
        )
        
        if result_text:
            output_str += f"**验证结果:** {result_text}\n"
        
        output_str += "-----------------------------------------\n\n"
        return output_str

    def run_single_test(self):
        self._clear_and_set_title("单个测试")
        
        try:
            # 你的原始单个测试参数
            interference = ['1', '1', '3', 5e7, 0, 40]
            with torch.no_grad():
                action = waveDecide_RL(interference, Normalized=False)
            
            formatted_output = self.format_results(interference, action)
            self.results_text.insert(tk.END, formatted_output)
            
        except Exception as e:
            messagebox.showerror("错误", f"单个测试中发生错误: {e}")
            self.results_text.insert(tk.END, f"发生错误: {e}\n")
        finally:
            self.results_text.config(state=tk.DISABLED)

    def run_batch_test(self):
        self._clear_and_set_title("批量测试")
        
        db_path = os.path.join('data', 'test_data.db')
        total_correct = 0
        total_evaluated = 0
        
        conn = None
        cursor = None
        
        try:
            all_states = get_all_test_states(db_path)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            batch_report_str = ""
            for state in all_states:
                interference = [state[0], state[1], state[2], state[3], state[4], state[5]]
                
                with torch.no_grad():
                    action = waveDecide_RL(interference, Normalized=True)
                
                is_correct = verify_in_db_efficient(state, action, cursor)
                
                result_text = "✅ 正确" if is_correct else "❌ 错误"
                if is_correct:
                    total_correct += 1
                
                total_evaluated += 1
                
                # batch_report_str += self.format_results(interference, action, result_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"批量评估中发生错误: {e}")
            self.results_text.insert(tk.END, f"\n批量评估中发生错误: {e}\n")
            return
        finally:
            if cursor: cursor.close()
            if conn: conn.close()

        self.results_text.insert(tk.END, batch_report_str)
        
        accuracy = total_correct / total_evaluated if total_evaluated > 0 else 0.0
        self.results_text.insert(tk.END, f"================================\n")
        self.results_text.insert(tk.END, f"模型在测试数据库上的正确率为: {accuracy:.2f} ({total_correct} / {total_evaluated})\n")
        self.results_text.config(state=tk.DISABLED)

    def run_manual_test(self):
        self._clear_and_set_title("手动测试")

        try:
            system = float(self.entries["干扰体制"].get())
            style = float(self.entries["干扰样式"].get())
            module = float(self.entries["调制方式"].get())
            freq = float(self.entries["干扰信号中心频率"].get())
            bandwidth = float(self.entries["干扰信号带宽"].get())
            power = float(self.entries["干扰信号电平"].get())

            interference = [system, style, module, freq, bandwidth, power]
            
            with torch.no_grad():
                action = waveDecide_RL(interference, Normalized=False)
            
            formatted_output = self.format_results(interference, action)
            self.results_text.insert(tk.END, formatted_output)
            
        except ValueError:
            messagebox.showerror("输入错误", "请确保所有参数为有效数字。")
        except Exception as e:
            messagebox.showerror("错误", f"手动测试中发生错误: {e}")
        finally:
            self.results_text.config(state=tk.DISABLED)

if __name__ == '__main__':
    root = tk.Tk()
    app = TestApp(root)
    root.mainloop()