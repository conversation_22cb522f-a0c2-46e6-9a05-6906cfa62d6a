#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整运行时间的脚本 - 模拟从点击按钮到UI显示结果的完整过程
"""

import time
import torch
import numpy as np
from ui import WaveformLookup
from waveDecide_RL import load_policy_model_auto

def simulate_complete_workflow():
    """模拟完整的UI工作流程"""
    
    print("=" * 70)
    print("完整运行时间测试 - 模拟从点击按钮到UI显示结果的完整过程")
    print("=" * 70)
    
    # 预加载模型（模拟程序启动）
    print("\n🚀 步骤1: 预加载模型（程序启动时）")
    preload_start = time.time()
    policy_model = load_policy_model_auto('logs/sacd-seed0-20250703-111238/model/best/policy.pth')
    waveform_lookup = WaveformLookup()
    preload_time = time.time() - preload_start
    print(f"   模型预加载时间: {preload_time:.4f} 秒")
    
    # 定义完整的测试函数
    def complete_single_test(interference, policy_model, waveform_lookup):
        """模拟完整的单个测试流程"""
        
        # 步骤1: UI清理和标题设置（模拟_clear_and_set_title）
        ui_setup_start = time.time()
        # 模拟UI操作
        time.sleep(0.001)  # 模拟UI操作延迟
        ui_setup_time = time.time() - ui_setup_start
        
        # 步骤2: 模型推理
        inference_start = time.time()
        
        # 数据预处理
        system = int(interference[0])
        style = int(interference[1])
        module = int(interference[2])
        freq = interference[3]
        bandwidth = interference[4]
        power = interference[5]

        # 归一化处理
        freq = np.round(np.float64((freq - 3e7) / (25e8 - 3e7)), 6)
        bandwidth = np.round(np.float64(bandwidth / 655e6), 6)
        power = np.round(np.float64((power - (-100)) / (100 - (-100))), 6)
        if module == None:
            module = 0

        # 模型推理
        state = np.array([system, style, module, freq, bandwidth, power])
        state = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
        with torch.no_grad():
            action = policy_model.act(state) + 1
        action = action.item()
        
        inference_time = time.time() - inference_start
        
        # 步骤3: CSV查询波形参数
        csv_query_start = time.time()
        waveform_info = waveform_lookup.get_waveform_info(action)
        csv_query_time = time.time() - csv_query_start
        
        # 步骤4: 结果格式化
        format_start = time.time()
        
        output_str = (
            f"**测试状态:**\n"
            f"  干扰体制: {interference[0]}\n"
            f"  干扰样式: {interference[1]}\n"
            f"  调制方式: {interference[2]}\n"
            f"  干扰信号中心频率: {interference[3]} Hz\n"
            f"  干扰信号带宽: {interference[4]} Hz\n"
            f"  干扰信号电平: {interference[5]} V\n"
            f"\n"
            f"**模型决策:** \n"
            f"  波形标签: {action} （共100个波形）\n"
        )
        
        if waveform_info:
            output_str += (
                f"\n"
                f"**选择波形参数:**\n"
                f"  调制方式: {waveform_info['调制方式']}\n"
                f"  工作模式: {waveform_info['工作模式']}\n"
                f"  工作频段(Hz): {waveform_info['工作频段']}\n"
                f"  信道带宽(Hz): {waveform_info['信道带宽']}\n"
                f"  信息速率: {waveform_info['信息速率']}\n"
                f"  扩频因子: {waveform_info['扩频因子']}\n"
                f"  跳频速率: {waveform_info['跳频速率']}\n"
                f"  跳频频率集: {waveform_info['跳频频率集']}\n"
                f"  编码方式: {waveform_info['编码方式']}\n"
                f"  多址方式: {waveform_info['多址方式']}\n"
            )
        
        output_str += "-----------------------------------------\n\n"
        
        format_time = time.time() - format_start
        
        # 步骤5: UI显示（模拟insert操作）
        ui_display_start = time.time()
        # 模拟UI显示操作
        time.sleep(0.001)  # 模拟UI显示延迟
        ui_display_time = time.time() - ui_display_start
        
        return {
            'action': action,
            'ui_setup_time': ui_setup_time,
            'inference_time': inference_time,
            'csv_query_time': csv_query_time,
            'format_time': format_time,
            'ui_display_time': ui_display_time,
            'output_str': output_str
        }
    
    # 测试参数
    test_cases = [
        ['1', '1', '3', 5e7, 0, 40],
        ['2', '3', '5', 1e8, 1e6, 50],
        ['3', '2', '7', 2e8, 5e5, 30]
    ]
    
    print(f"\n🧪 步骤2: 运行完整测试流程")
    
    for i, interference in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i} ---")
        
        # 记录完整流程时间
        total_start = time.time()
        
        result = complete_single_test(interference, policy_model, waveform_lookup)
        
        total_time = time.time() - total_start
        
        print(f"干扰参数: {interference}")
        print(f"决策结果: 波形 {result['action']}")
        print(f"")
        print(f"⏱️  时间分解:")
        print(f"   UI设置时间:    {result['ui_setup_time']*1000:.2f} ms")
        print(f"   模型推理时间:  {result['inference_time']*1000:.2f} ms")
        print(f"   CSV查询时间:   {result['csv_query_time']*1000:.2f} ms")
        print(f"   结果格式化时间: {result['format_time']*1000:.2f} ms")
        print(f"   UI显示时间:    {result['ui_display_time']*1000:.2f} ms")
        print(f"   ─────────────────────────────")
        print(f"   总时间:       {total_time*1000:.2f} ms ({total_time:.4f} 秒)")
        
        # 验证是否符合7ms的预期
        if total_time * 1000 <= 7:
            print(f"   ✅ 符合7ms预期")
        else:
            print(f"   ⚠️  超过7ms预期")
    
    # 多次测试统计
    print(f"\n📊 步骤3: 多次测试统计（使用第一个测试案例）")
    interference = test_cases[0]
    times = []
    
    for i in range(10):
        total_start = time.time()
        result = complete_single_test(interference, policy_model, waveform_lookup)
        total_time = time.time() - total_start
        times.append(total_time * 1000)  # 转换为毫秒
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"   运行10次统计:")
    print(f"   平均时间: {avg_time:.2f} ms")
    print(f"   最短时间: {min_time:.2f} ms")
    print(f"   最长时间: {max_time:.2f} ms")
    print(f"   时间范围: {min_time:.2f} - {max_time:.2f} ms")
    
    if avg_time <= 7:
        print(f"   ✅ 平均时间符合7ms预期")
    else:
        print(f"   ⚠️  平均时间超过7ms预期")

if __name__ == "__main__":
    simulate_complete_workflow()
