#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行时间统计功能的脚本
"""

import time
import torch
from waveDecide_RL import waveDecide_RL, load_policy_model_auto

def test_original_vs_preloaded():
    """测试原始方法和预加载方法的性能差异"""
    
    # 测试参数
    interference = ['1', '1', '3', 5e7, 0, 40]
    
    print("=" * 60)
    print("测试运行时间统计功能")
    print("=" * 60)
    
    # 测试原始方法（每次都加载模型）
    print("\n1. 测试原始方法（每次都加载模型）:")
    times_original = []
    for i in range(3):
        start_time = time.time()
        with torch.no_grad():
            action = waveDecide_RL(interference, Normalized=False)
        execution_time = time.time() - start_time
        times_original.append(execution_time)
        print(f"   第{i+1}次运行: {execution_time:.4f} 秒, 结果: {action}")
    
    avg_original = sum(times_original) / len(times_original)
    print(f"   平均时间: {avg_original:.4f} 秒")
    
    # 测试预加载方法
    print("\n2. 测试预加载方法:")
    print("   正在预加载模型...")
    preload_start = time.time()
    policy_model = load_policy_model_auto('logs/sacd-seed0-20250703-111238/model/best/policy.pth')
    preload_time = time.time() - preload_start
    print(f"   模型预加载时间: {preload_time:.4f} 秒")
    
    # 使用预加载的模型进行推理
    def waveDecide_RL_preloaded(interference, policy_model, Normalized=False):
        import numpy as np
        
        system = int(interference[0])
        style = int(interference[1])
        module = int(interference[2])
        freq = interference[3]
        bandwidth = interference[4]
        power = interference[5]

        if not Normalized:
            freq = np.round(np.float64((freq - 3e7) / (25e8 - 3e7)), 6)
            bandwidth = np.round(np.float64(bandwidth / 655e6), 6)
            power = np.round(np.float64((power - (-100)) / (100 - (-100))), 6)
        if module == None:
            module = 0

        state = np.array([system, style, module, freq, bandwidth, power])
        state = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
        with torch.no_grad():
            action = policy_model.act(state) + 1
        return action.item()
    
    times_preloaded = []
    for i in range(3):
        start_time = time.time()
        with torch.no_grad():
            action = waveDecide_RL_preloaded(interference, policy_model, Normalized=False)
        execution_time = time.time() - start_time
        times_preloaded.append(execution_time)
        print(f"   第{i+1}次运行: {execution_time:.4f} 秒, 结果: {action}")
    
    avg_preloaded = sum(times_preloaded) / len(times_preloaded)
    print(f"   平均时间: {avg_preloaded:.4f} 秒")
    
    # 性能对比
    print("\n3. 性能对比:")
    print(f"   原始方法平均时间: {avg_original:.4f} 秒")
    print(f"   预加载方法平均时间: {avg_preloaded:.4f} 秒")
    print(f"   性能提升: {(avg_original - avg_preloaded) / avg_original * 100:.1f}%")
    print(f"   加速比: {avg_original / avg_preloaded:.1f}x")
    
    print("\n4. 总结:")
    print(f"   - 模型预加载一次需要: {preload_time:.4f} 秒")
    print(f"   - 如果运行超过 {int(preload_time / (avg_original - avg_preloaded))} 次测试，预加载方法更有优势")
    print(f"   - 首次运行时间从 {avg_original:.4f} 秒减少到 {avg_preloaded:.4f} 秒")

if __name__ == "__main__":
    test_original_vs_preloaded()
