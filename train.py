import os
import yaml # 用于处理 YAML（YAML Ain't Markup Language）格式数据
import argparse # 用于解析命令行参数
from datetime import datetime

from sacd.env import WaveDecideEnv
from sacd.agent import SacdAgent


def run(args):
    with open(args.config, encoding = 'utf-8') as f:
        config = yaml.load(f, Loader=yaml.SafeLoader)

    # Create environments.
    env = WaveDecideEnv() 
    test_env = WaveDecideEnv()

    # Specify the directory to log.
    name = os.path.splitext(os.path.basename(args.config))[0]
    time = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_dir = os.path.join(
        'logs', f'{name}-seed{args.seed}-{time}')

    # Create the agent.
    agent = SacdAgent(
        env=env, test_env=test_env, log_dir=log_dir, cuda=args.cuda,
        seed=args.seed, **config)
    agent.run()


# __name__ 是一个特殊变量，当模块被直接运行时，其值为 '__main__'
if __name__ == '__main__':
    parser = argparse.ArgumentParser()  # 通过创建这个对象，你可以定义程序可以接受的命令行参数，并解析这些参数
    parser.add_argument('--config', type=str, default=os.path.join('config', 'sacd.yaml'))  # 用于指定配置文件的路径
    parser.add_argument('--cuda', action='store_true')  # 一个布尔类型的参数，用于指示是否使用CUDA加速
    parser.add_argument('--seed', type=int, default=0)  # 用于指定随机种子，默认值为 0 。这个参数用于确保实验的可重复性。
    args = parser.parse_args()  # 读取命令行输入的参数，并将其解析为一个命名空间对象 args
    run(args)
