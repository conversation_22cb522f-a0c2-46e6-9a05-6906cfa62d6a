#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本 - 验证所有时间统计功能
"""

import time
import torch
import numpy as np
from ui import WaveformLookup
from waveDecide_RL import load_policy_model_auto

def test_final_implementation():
    """测试最终的实现"""
    
    print("=" * 70)
    print("🎯 最终时间统计功能测试")
    print("=" * 70)
    
    # 预加载模型
    print("\n🚀 步骤1: 预加载模型")
    preload_start = time.time()
    policy_model = load_policy_model_auto('logs/sacd-seed0-20250703-111238/model/best/policy.pth')
    waveform_lookup = WaveformLookup()
    preload_time = time.time() - preload_start
    print(f"   模型预加载时间: {preload_time*1000:.2f} ms")
    
    def waveDecide_RL_with_preloaded_model(interference, policy_model, Normalized=False):
        """使用预加载的模型进行波形决策"""
        system = int(interference[0])
        style = int(interference[1])
        module = int(interference[2])
        freq = interference[3]
        bandwidth = interference[4]
        power = interference[5]

        if not Normalized:
            freq = np.round(np.float64((freq - 3e7) / (25e8 - 3e7)), 6)
            bandwidth = np.round(np.float64(bandwidth / 655e6), 6)
            power = np.round(np.float64((power - (-100)) / (100 - (-100))), 6)
        if module == None:
            module = 0

        state = np.array([system, style, module, freq, bandwidth, power])
        state = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
        with torch.no_grad():
            action = policy_model.act(state) + 1
        return action.item()

    def format_results_with_timing(interference, action, waveform_lookup, inference_time):
        """格式化结果并包含时间信息"""
        waveform_info = waveform_lookup.get_waveform_info(action)
        
        output_str = (
            f"**测试状态:**\n"
            f"  干扰体制: {interference[0]}\n"
            f"  干扰样式: {interference[1]}\n"
            f"  调制方式: {interference[2]}\n"
            f"  干扰信号中心频率: {interference[3]} Hz\n"
            f"  干扰信号带宽: {interference[4]} Hz\n"
            f"  干扰信号电平: {interference[5]} V\n"
            f"\n"
            f"**模型决策:** \n"
            f"  波形标签: {action} （共100个波形）\n"
            f"  模型推理时间: {inference_time*1000:.2f} ms\n"
        )
        
        if waveform_info:
            output_str += (
                f"\n"
                f"**选择波形参数:**\n"
                f"  调制方式: {waveform_info['调制方式']}\n"
                f"  工作模式: {waveform_info['工作模式']}\n"
                f"  工作频段(Hz): {waveform_info['工作频段']}\n"
                f"  信道带宽(Hz): {waveform_info['信道带宽']}\n"
            )
        
        return output_str

    # 测试案例
    test_cases = [
        (['1', '1', '3', 5e7, 0, 40], "单个测试案例"),
        (['2', '3', '5', 1e8, 1e6, 50], "手动测试案例"),
        (['3', '2', '7', 2e8, 5e5, 30], "额外测试案例")
    ]
    
    print(f"\n🧪 步骤2: 模拟完整UI流程测试")
    
    for i, (interference, case_name) in enumerate(test_cases, 1):
        print(f"\n--- {case_name} ---")
        
        # 模拟完整UI流程
        total_start = time.time()
        
        # 1. UI清理和设置 (模拟_clear_and_set_title)
        ui_setup_start = time.time()
        time.sleep(0.001)  # 模拟UI操作
        ui_setup_time = time.time() - ui_setup_start
        
        # 2. 模型推理
        inference_start = time.time()
        action = waveDecide_RL_with_preloaded_model(interference, policy_model, Normalized=False)
        inference_time = time.time() - inference_start
        
        # 3. 结果格式化
        format_start = time.time()
        formatted_output = format_results_with_timing(interference, action, waveform_lookup, inference_time)
        format_time = time.time() - format_start
        
        # 4. UI显示 (模拟insert操作)
        ui_display_start = time.time()
        time.sleep(0.001)  # 模拟UI显示
        ui_display_time = time.time() - ui_display_start
        
        total_time = time.time() - total_start
        
        print(f"干扰参数: {interference}")
        print(f"决策结果: 波形 {action}")
        print(f"")
        print(f"⏱️  时间分解:")
        print(f"   UI设置时间:    {ui_setup_time*1000:.2f} ms")
        print(f"   模型推理时间:  {inference_time*1000:.2f} ms")
        print(f"   结果格式化时间: {format_time*1000:.2f} ms")
        print(f"   UI显示时间:    {ui_display_time*1000:.2f} ms")
        print(f"   ─────────────────────────────")
        print(f"   总时间:       {total_time*1000:.2f} ms")
        
        # 验证推理时间是否符合7ms预期
        if inference_time * 1000 <= 7:
            print(f"   ✅ 推理时间符合7ms预期")
        else:
            print(f"   ⚠️  推理时间超过7ms预期")
    
    # 专门测试推理时间
    print(f"\n📊 步骤3: 专门测试推理时间（100次）")
    interference = test_cases[0][0]
    inference_times = []
    
    for i in range(100):
        inference_start = time.time()
        action = waveDecide_RL_with_preloaded_model(interference, policy_model, Normalized=False)
        inference_time = time.time() - inference_start
        inference_times.append(inference_time * 1000)  # 转换为毫秒
    
    avg_inference = sum(inference_times) / len(inference_times)
    min_inference = min(inference_times)
    max_inference = max(inference_times)
    
    print(f"   推理时间统计（100次）:")
    print(f"   平均推理时间: {avg_inference:.2f} ms")
    print(f"   最短推理时间: {min_inference:.2f} ms")
    print(f"   最长推理时间: {max_inference:.2f} ms")
    print(f"   时间范围: {min_inference:.2f} - {max_inference:.2f} ms")
    
    if avg_inference <= 7:
        print(f"   ✅ 平均推理时间符合7ms预期")
    else:
        print(f"   ⚠️  平均推理时间超过7ms预期")
    
    print(f"\n🎉 总结:")
    print(f"   - 模型预加载时间: {preload_time*1000:.2f} ms")
    print(f"   - 平均推理时间: {avg_inference:.2f} ms")
    print(f"   - 完整UI流程时间: ~16-31 ms")
    print(f"   - 推理时间占比: {avg_inference/30*100:.1f}% (假设总时间30ms)")
    print(f"   - 性能符合预期: {'✅' if avg_inference <= 7 else '⚠️'}")

if __name__ == "__main__":
    test_final_implementation()
