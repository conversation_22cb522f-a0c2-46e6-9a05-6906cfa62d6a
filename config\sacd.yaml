num_steps: 300000             # 总训练步数
batch_size: 64                # 抽取样本的批次大小
lr: 0.0003                    # 学习率  
memory_size: 300000           # 经验回放池大小
gamma: 0.99                   # 折扣因子
multi_step: 1                 # 每隔多少步记录奖励
target_entropy_ratio: 0.98    # 目标熵
start_steps: 20000            # 开始训练的步数
update_interval: 4            # 更新网络的频率
target_update_interval: 4     # 更新目标网络的频率
dueling_net: True             # 是否使用对偶Q网络
num_eval_steps: 10000         # 评估的最大步数
max_episode_steps: 27000      # 每个episode的最大步数
log_interval: 10              # 记录日志的频率
eval_interval: 10000          # 评估的频率